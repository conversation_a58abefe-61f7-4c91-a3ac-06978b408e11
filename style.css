/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #212121;
    color: #ffffff;
    overflow: hidden;
}

.container-fluid {
    padding: 0;
}

/* Sidebar Styles */
.sidebar {
    background-color: #171717;
    width: 60px;
    min-height: 100vh;
    padding: 20px 0;
    border-right: 1px solid #2f2f2f;
}

.sidebar-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.brand-section {
    margin-bottom: 30px;
}

.brand-icon {
    font-size: 24px;
    color: #ffffff;
}

.nav-icons {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.nav-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: #8e8ea0;
}

.nav-item:hover {
    background-color: #2f2f2f;
    color: #ffffff;
}

.nav-item.active {
    background-color: #2f2f2f;
    color: #ffffff;
}

.sidebar-bottom {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 20px;
}

/* Main Content Styles */
.main-content {
    background-color: #212121;
    min-height: 100vh;
    padding: 0;
}

/* Header Styles */
.header {
    padding: 20px 30px;
    border-bottom: 1px solid #2f2f2f;
}

.app-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: #ffffff;
}

.header-left i {
    color: #8e8ea0;
    font-size: 14px;
    cursor: pointer;
}

.btn-plus {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-plus:hover {
    background: linear-gradient(135deg, #5855eb, #7c3aed);
    color: white;
    transform: translateY(-1px);
}

/* Chat Area Styles */
.chat-area {
    padding: 40px;
}

.welcome-section {
    max-width: 600px;
}

.welcome-title {
    font-size: 32px;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 0;
}

/* Input Section Styles */
.input-section {
    padding: 20px 30px 30px;
}

.input-container {
    max-width: 800px;
    margin: 0 auto;
}

.input-wrapper {
    background-color: #2f2f2f;
    border-radius: 25px;
    padding: 12px 20px;
    border: 1px solid #404040;
    transition: border-color 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: #6366f1;
}

.btn-attachment {
    background: none;
    border: none;
    color: #8e8ea0;
    padding: 8px;
    margin-right: 10px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.btn-attachment:hover {
    background-color: #404040;
    color: #ffffff;
}

.chat-input {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    flex-grow: 1;
    padding: 8px 0;
}

.chat-input:focus {
    outline: none;
    box-shadow: none;
}

.chat-input::placeholder {
    color: #8e8ea0;
}

.input-actions {
    gap: 10px;
    margin-left: 10px;
}

.tank-label {
    color: #8e8ea0;
    font-size: 14px;
    padding: 4px 8px;
    background-color: #404040;
    border-radius: 12px;
    font-weight: 500;
}

.btn-mic, .btn-send {
    background: none;
    border: none;
    color: #8e8ea0;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-mic:hover, .btn-send:hover {
    background-color: #404040;
    color: #ffffff;
}

.btn-send {
    background-color: #ffffff;
    color: #212121;
}

.btn-send:hover {
    background-color: #e5e5e5;
    color: #212121;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 50px;
    }
    
    .header {
        padding: 15px 20px;
    }
    
    .app-title {
        font-size: 18px;
    }
    
    .welcome-title {
        font-size: 24px;
    }
    
    .input-section {
        padding: 15px 20px 20px;
    }
    
    .input-wrapper {
        padding: 10px 15px;
    }
}
